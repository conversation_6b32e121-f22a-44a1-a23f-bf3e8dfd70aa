import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON>cle, Circle, Loader2, <PERSON>, Zap, Cpu, Download, ChevronDown, ChevronUp, Type, Sparkles } from 'lucide-react';

interface ProgressStage {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  progress: number;
  status: 'pending' | 'active' | 'completed' | 'error';
  estimatedTime?: string;
}

interface ProgressBarProps {
  isVisible: boolean;
  isDarkMode: boolean;
  sessionId?: string;
  generationMode?: 'text-to-3d' | 'image-to-3d';
  delighterEnabled?: boolean;
  onComplete?: () => void;
  onError?: (error: string) => void;
}

const defaultStages: ProgressStage[] = [
  {
    id: 'text_to_image',
    name: 'Text-to-Image Generation',
    description: 'Generating image from text prompt using AI',
    icon: <Type className="w-4 h-4" />,
    progress: 0,
    status: 'pending',
    estimatedTime: '5s'
  },
  {
    id: 'preprocessing',
    name: 'Image Preprocessing',
    description: 'Loading image and initializing pipeline',
    icon: <Circle className="w-4 h-4" />,
    progress: 0,
    status: 'pending',
    estimatedTime: '5s'
  },
  {
    id: 'sparse_structure',
    name: 'Sparse Structure Sampling',
    description: 'Generating 3D structure foundation (12 steps)',
    icon: <Cpu className="w-4 h-4" />,
    progress: 0,
    status: 'pending',
    estimatedTime: '25s'
  },
  {
    id: 'slat_generation',
    name: 'SLAT Generation Sampling',
    description: 'Creating detailed 3D representation (12 steps)',
    icon: <Zap className="w-4 h-4" />,
    progress: 0,
    status: 'pending',
    estimatedTime: '10s'
  },
  {
    id: 'mesh_creation',
    name: 'Mesh Processing',
    description: 'Decimating mesh and rendering',
    icon: <Cpu className="w-4 h-4" />,
    progress: 0,
    status: 'pending',
    estimatedTime: '15s'
  },
  {
    id: 'texture_generation',
    name: 'Texture Generation',
    description: 'Optimizing texture baking (2500 steps)',
    icon: <Sparkles className="w-4 h-4" />,
    progress: 0,
    status: 'pending',
    estimatedTime: '30s'
  },
  {
    id: 'glb_export',
    name: 'GLB Export',
    description: 'Finalizing and exporting 3D model',
    icon: <Download className="w-4 h-4" />,
    progress: 0,
    status: 'pending',
    estimatedTime: '3s'
  },
  {
    id: 'download',
    name: 'Model Download',
    description: 'Downloading generated 3D model',
    icon: <Download className="w-4 h-4" />,
    progress: 0,
    status: 'pending',
    estimatedTime: '5s'
  },
  {
    id: 'texture_enhancement',
    name: 'Texture Enhancement',
    description: 'Enhancing textures with Agisoft De-Lighter',
    icon: <Sparkles className="w-4 h-4" />,
    progress: 0,
    status: 'pending',
    estimatedTime: '10s'
  },
  {
    id: 'hunyaun',
    name: 'Hunyuan3D-2 Generation',
    description: 'Generating 3D model with Hunyuan3D-2 pipeline',
    icon: <Cpu className="w-4 h-4" />,
    progress: 0,
    status: 'pending',
    estimatedTime: '60s'
  }
];

export const ProgressBar: React.FC<ProgressBarProps> = ({
  isVisible,
  isDarkMode,
  sessionId,
  generationMode = 'image-to-3d',
  delighterEnabled = false,
  onComplete,
  onError
}) => {
  const electronProgressDetected = React.useRef(false);

  // Filter stages based on generation mode and delighter setting
  const getStagesForMode = (mode: 'text-to-3d' | 'image-to-3d', includeDelighter: boolean = false) => {
    let stages = defaultStages;

    if (mode === 'image-to-3d') {
      // Skip text-to-image stage for image-to-3d mode
      stages = stages.filter(stage => stage.id !== 'text_to_image');
    }

    if (!includeDelighter) {
      // Skip texture enhancement stage if delighter is not enabled
      stages = stages.filter(stage => stage.id !== 'texture_enhancement');
    }

    return stages;
  };

  // Detect pipeline type based on received progress updates
  const [detectedPipeline, setDetectedPipeline] = useState<'trellis' | 'hunyuan' | null>(null);

  const [stages, setStages] = useState<ProgressStage[]>(getStagesForMode(generationMode, delighterEnabled));
  const [currentStageIndex, setCurrentStageIndex] = useState(0);
  const [overallProgress, setOverallProgress] = useState(0);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [estimatedTimeRemaining, setEstimatedTimeRemaining] = useState('');
  const [isExpanded, setIsExpanded] = useState(false);

  // Timer for elapsed time
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isVisible && currentStageIndex < stages.length) {
      interval = setInterval(() => {
        setElapsedTime(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isVisible, currentStageIndex, stages.length]);

  // Calculate overall progress
  useEffect(() => {
    const completedStages = stages.filter(stage => stage.status === 'completed').length;
    const activeStage = stages.find(stage => stage.status === 'active');
    const activeProgress = activeStage ? activeStage.progress : 0;

    const overall = ((completedStages + (activeProgress / 100)) / stages.length) * 100;
    setOverallProgress(Math.min(overall, 100));
  }, [stages]);

  // Disable SSE & polling if we are receiving electron progress
  const skipNetworkProgress = electronProgressDetected.current;

  // Real-time progress updates using Server-Sent Events
  useEffect(() => {
    if (skipNetworkProgress || !isVisible || !sessionId) {
      // Reset when not visible - use correct stages for mode
      setStages(getStagesForMode(generationMode, delighterEnabled));
      setCurrentStageIndex(0);
      setOverallProgress(0);
      setElapsedTime(0);
      return;
    }

    // Reset progress state when starting a new session
    setStages(getStagesForMode(generationMode, delighterEnabled));
    setCurrentStageIndex(0);
    setOverallProgress(0);
    setElapsedTime(0);

    // Skip HTTP-based progress in Electron app - use IPC instead
    console.log(`[DEBUG] Progress tracking for session: ${sessionId} - using IPC communication only`);

    let pollingInterval: NodeJS.Timeout;

    // HTTP polling disabled in Electron app - using IPC instead

    // SSE connection disabled in Electron app - using IPC instead

    return () => {
      clearInterval(pollingInterval);
    };
  }, [skipNetworkProgress, isVisible, sessionId, generationMode, delighterEnabled, onComplete, onError]);

  // Calculate estimated time remaining
  useEffect(() => {
    const remainingStages = stages.slice(currentStageIndex);
    const totalEstimated = remainingStages.reduce((total, stage) => {
      const time = parseInt(stage.estimatedTime?.replace('s', '') || '0');
      return total + time;
    }, 0);

    const currentStageProgress = stages[currentStageIndex]?.progress || 0;
    const currentStageTime = parseInt(stages[currentStageIndex]?.estimatedTime?.replace('s', '') || '0');
    const currentStageRemaining = (currentStageTime * (100 - currentStageProgress)) / 100;

    const totalRemaining = Math.max(0, totalEstimated - currentStageTime + currentStageRemaining);
    setEstimatedTimeRemaining(totalRemaining > 0 ? `${Math.ceil(totalRemaining)}s` : '');
  }, [stages, currentStageIndex]);

  // Listen to Electron backend progress events as an alternative to SSE
  useEffect(() => {
    console.log('[ProgressBar] Setting up progress listener - skipNetworkProgress:', skipNetworkProgress, 'isVisible:', isVisible, 'sessionId:', sessionId);
    if (skipNetworkProgress || !isVisible || !sessionId) return;

    const removeListener = (window as any).electronAPI?.onPipelineStatus?.((status: any) => {
      console.log('[ProgressBar] Received pipeline status:', status);
      console.log('[ProgressBar] Expected sessionId:', sessionId);

      if (!status || typeof status !== 'object') {
        console.log('[ProgressBar] Invalid status object');
        return;
      }
      if (status.event !== 'progress') {
        console.log('[ProgressBar] Not a progress event:', status.event);
        return;
      }
      if (status.session_id !== sessionId) {
        console.log('[ProgressBar] Session ID mismatch - received:', status.session_id, 'expected:', sessionId);
        return;
      }

      console.log('[ProgressBar] Processing progress update - Stage:', status.stage, 'Progress:', status.progress, 'Overall:', status.overall_progress);

      // Update overall progress if available
      if (status.overall_progress !== undefined) {
        setOverallProgress(Math.min(100, status.overall_progress));
      }

      // Map incoming stage id to default stage list with enhanced progress data
      setStages(prev => prev.map(stage => {
        if (stage.id === status.stage) {
          const newProgress = Math.min(100, status.stage_progress || status.progress || 0);
          console.log('[ProgressBar] Updating stage:', stage.id, 'to progress:', newProgress, 'Step:', status.step, '/', status.total);

          // Create enhanced description with step information
          let enhancedDescription = status.message || status.description || stage.description;
          if (status.step && status.total && status.step > 0) {
            enhancedDescription += ` (${status.step}/${status.total})`;
          }

          return {
            ...stage,
            progress: newProgress,
            status: newProgress >= 100 ? 'completed' : (newProgress > 0 ? 'active' : 'pending'),
            description: enhancedDescription,
          } as any;
        }
        return stage;
      }));
    });

    return () => {
      if (removeListener) removeListener();
    };
  }, [skipNetworkProgress, isVisible, sessionId, generationMode, delighterEnabled]);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return mins > 0 ? `${mins}m ${secs}s` : `${secs}s`;
  };

  if (!isVisible) return null;

  const currentStage = stages[currentStageIndex];
  const currentStageName = currentStage?.name || 'Initializing...';

  return (
    <div className={`relative w-full rounded-lg border ${
      isDarkMode
        ? 'bg-gray-800 border-gray-700'
        : 'bg-white border-gray-200'
    } shadow-lg overflow-visible`}>
      {/* Compact Header - Always Visible */}
      <div className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Loader2 className={`w-5 h-5 animate-spin ${
              isDarkMode ? 'text-blue-400' : 'text-blue-600'
            }`} />
            <div className="flex flex-col">
              <h3 className={`text-sm font-semibold ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                Generating 3D Model
              </h3>
              <p className={`text-xs ${
                isDarkMode ? 'text-gray-400' : 'text-gray-500'
              }`}>
                {currentStageName}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <div className="flex items-center gap-4 text-xs">
              <div className={`flex items-center gap-1 ${
                isDarkMode ? 'text-gray-300' : 'text-gray-600'
              }`}>
                <Clock className="w-3 h-3" />
                <span>{formatTime(elapsedTime)}</span>
              </div>
              <span className={`font-medium ${
                isDarkMode ? 'text-blue-400' : 'text-blue-600'
              }`}>
                {Math.round(overallProgress)}%
              </span>
            </div>

            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className={`p-1 rounded hover:bg-opacity-20 transition-colors ${
                isDarkMode
                  ? 'text-gray-400 hover:bg-gray-600 hover:text-gray-300'
                  : 'text-gray-500 hover:bg-gray-200 hover:text-gray-700'
              }`}
              aria-label={isExpanded ? 'Collapse details' : 'Expand details'}
            >
              {isExpanded ? (
                <ChevronUp className="w-4 h-4" />
              ) : (
                <ChevronDown className="w-4 h-4" />
              )}
            </button>
          </div>
        </div>

        {/* Compact Progress Bar */}
        <div className="mt-3">
          <div className={`w-full h-2 rounded-full ${
            isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
          }`}>
            <div
              className={`h-full rounded-full transition-all duration-300 ${
                isDarkMode
                  ? 'bg-gradient-to-r from-blue-500 to-purple-500'
                  : 'bg-gradient-to-r from-blue-600 to-purple-600'
              }`}
              style={{ width: `${overallProgress}%` }}
            />
          </div>
        </div>
      </div>

      {/* Expandable Details - Positioned absolutely to expand downward only */}
      {isExpanded && (
        <div className={`absolute top-full left-0 right-0 z-10 rounded-b-lg border-t-0 border ${
          isDarkMode
            ? 'bg-gray-800 border-gray-700'
            : 'bg-white border-gray-200'
        } shadow-lg`}>
          <div className={`border-t px-4 pb-4 ${
            isDarkMode ? 'border-gray-700' : 'border-gray-200'
          }`}>
            <div className="pt-4 space-y-3">
              {stages.map((stage, index) => (
                <div key={stage.id} className="flex items-center gap-3">
                  {/* Status Icon */}
                  <div className={`flex-shrink-0 ${
                    stage.status === 'completed' ? (isDarkMode ? 'text-green-400' : 'text-green-600') :
                    stage.status === 'active' ? (isDarkMode ? 'text-blue-400' : 'text-blue-600') :
                    stage.status === 'error' ? (isDarkMode ? 'text-red-400' : 'text-red-600') :
                    (isDarkMode ? 'text-gray-500' : 'text-gray-400')
                  }`}>
                    {stage.status === 'completed' ? (
                      <CheckCircle className="w-4 h-4" />
                    ) : stage.status === 'active' ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <div className="w-4 h-4 flex items-center justify-center">
                        {React.cloneElement(stage.icon as React.ReactElement, { className: 'w-3 h-3' })}
                      </div>
                    )}
                  </div>

                  {/* Stage Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h4 className={`text-sm font-medium ${
                        stage.status === 'active' ? (isDarkMode ? 'text-white' : 'text-gray-900') :
                        stage.status === 'completed' ? (isDarkMode ? 'text-green-400' : 'text-green-600') :
                        (isDarkMode ? 'text-gray-400' : 'text-gray-500')
                      }`}>
                        {stage.name}
                      </h4>
                      {stage.status === 'active' && (
                        <span className={`text-xs font-medium ${
                          isDarkMode ? 'text-blue-400' : 'text-blue-600'
                        }`}>
                          {Math.round(stage.progress)}%
                        </span>
                      )}
                    </div>
                    <p className={`text-xs ${
                      isDarkMode ? 'text-gray-500' : 'text-gray-400'
                    }`}>
                      {stage.description}
                    </p>

                    {/* Stage Progress Bar */}
                    {stage.status === 'active' && (
                      <div className={`mt-2 w-full h-1.5 rounded-full ${
                        isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
                      }`}>
                        <div
                          className={`h-full rounded-full transition-all duration-200 ${
                            isDarkMode ? 'bg-blue-500' : 'bg-blue-600'
                          }`}
                          style={{ width: `${stage.progress}%` }}
                        />
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {estimatedTimeRemaining && (
              <div className={`mt-4 pt-3 border-t text-center text-xs ${
                isDarkMode ? 'border-gray-700 text-gray-400' : 'border-gray-200 text-gray-500'
              }`}>
                Estimated time remaining: {estimatedTimeRemaining}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
